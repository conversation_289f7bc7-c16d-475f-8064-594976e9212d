
98b894ea124ac16a4c20ccfcb6534647be833e7d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"bdf91a4a1e393f2d0761d4b3239cbc32\"}","integrity":"sha512-ou/x+e6gnNwT0at6mSrwqhSMF+1t9wTbTdxwAY651cDNmyWgGKxVlBI74XmhpbMHXJxz+rB4ZbFnh8Q+s08W0Q==","time":1754200395961,"size":1847299}