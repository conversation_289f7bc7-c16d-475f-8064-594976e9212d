
bea3825449409735f16358c9df214d9ef6ddd6dc	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"8de948c819f8c9dd3aa0ffe04ae46034\"}","integrity":"sha512-u0vC8hacko3PSnNWjyIzVlUCk8F+PX7pJETkTSTJEr4W4/na1lPb2MEt5WpXqpkv9sESJe7u9lGy4heudT+eDg==","time":1754200432870,"size":1848354}