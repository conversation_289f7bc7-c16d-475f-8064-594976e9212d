
df2b54c5b10d8329a7ec235223cc6c5d06cf360f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"867a6ad4148bf4c81ffb689acc3290ff\"}","integrity":"sha512-kRk3GKMXHAIWBi6fzSg8+Vv984F12z37fe70+2OBsbz2mKD5MCe0CZDQNj3+WwEQTEbVFXIo2uQeU6vWDLmgag==","time":1754200379703,"size":872004}