
aa4517914afde90a07210c2b3b89ca1df33941cc	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"5288dcb9710f3ce864c818049197f3e3\"}","integrity":"sha512-0wUTZtySywBxZNNMJC2zYogHgnGg3mANM2ZgwcCRpYrye+JwJ7lSfQ1As/ZEuANT0GPJh+/LMLxu5w5KJA+9VA==","time":1754200432696,"size":26759}