
8340e47eba292c6fef98c2c93b12896af38e537d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"c0cd28b05a255512d93d2d497d2c8f0a\"}","integrity":"sha512-L68cWN4L+cubVHBgtAu/hdZ+7yTH0PRj7qgBkqwJDqK5M1kZw0gWmxYIdolwpP3PkYoE2JYzqhlM62L+m/bLsg==","time":1754200376671,"size":26749}