
e86ef5019da6b8883b78f2841397d119ba5a88a5	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"bdf91a4a1e393f2d0761d4b3239cbc32\"}","integrity":"sha512-5eOfmrjClg2BLlfZ69QmBqAorAYNAr0QLLECSrkoacirn5ZL8gwdsKlHkqRayAFcyu0qu6ZrBgWjst+XTYgWAQ==","time":1754200398781,"size":871833}