<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <div class="time-display">
        <svg-icon icon-class="time" />
        {{ time }}
      </div>
      <div class="user-name">
        {{ name }}
      </div>
      <el-tooltip content="全屏显示" placement="bottom" effect="light">
        <screenfull
          id="screenfull"
          class="right-menu-item hover-effect"
          style="color: #3f9af8;font-size: 18px;"
        />
      </el-tooltip>
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <router-link to="/home/<USER>">
            <el-dropdown-item>
              主控台
            </el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="userCenter">
            <span style="display:block;">个人中心</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="changePassword">
            <span style="display:block;">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">注销</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull
  },
  data() {
    return {
      time: '',
      name: 'admin'
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar'
    ])
  },
  created() {
    setInterval(this.nowTime, 1000)
    this.name = this.$store.state.user.name
  },
  beforeDestroy() {
    if (this.nowTime) {
      clearInterval(this.nowTime) // 在Vue实例销毁前，清除时间定时器
    }
  },
  methods: {
    nowTime() {
      this.time = this.$moment(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss')
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      console.log(this.$store.state.user.token)
      // await this.$store.dispatch('user/logout')

      if (this.$store.state.user.token) {
        console.log('正常退出')
        await this.$store.dispatch('user/logout')
        this.$router.push(`/login`)
      } else {
        console.log('非正常退出reset')
        await this.$store.dispatch('user/resetToken')
        this.$router.push(`/login`)
      }
      // this.$store.state.tagsView.visitedViews = []
      // this.$store.state.tagsView.cachedViews = []
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)

      this.$router.push(`/login`)
    },
    userCenter() {
      this.$router.push(`/userCenter/index`)
    },
    changePassword() {
      this.$router.push('/userPassword/index')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.navbar {
  height: 60px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, $bgSecondary 0%, $bgPrimary 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid $borderPrimary;
  box-shadow: $shadowPrimary;

  // 确保与TagsView的视觉连贯性
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, $borderPrimary, transparent);
  }

  .hamburger-container {
    line-height: 56px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -webkit-tap-highlight-color: transparent;
    padding: 0 15px;
    border-radius: 8px;
    margin: 8px;

    &:hover {
      background: rgba(0, 212, 255, 0.1);
      transform: scale(1.05);
    }
  }

  .breadcrumb-container {
    float: left;
    margin-left: 10px;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 60px;
    display: flex;
    align-items: center;
    padding-right: 20px;

    &:focus {
      outline: none;
    }

    .time-display {
      display: flex;
      align-items: center;
      color: $textSecondary;
      font-weight: 500;
      margin-right: 20px;
      padding: 8px 16px;
      background: rgba(0, 212, 255, 0.1);
      border-radius: 20px;
      border: 1px solid rgba(0, 212, 255, 0.2);
      font-size: 14px;

      .svg-icon {
        margin-right: 8px;
        color: $techBlue;
      }
    }

    .user-name {
      color: $techBlue;
      font-weight: 600;
      margin-right: 20px;
      padding: 8px 16px;
      background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));
      border-radius: 20px;
      border: 1px solid rgba(0, 212, 255, 0.3);
      font-size: 14px;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 12px;
      height: 40px;
      line-height: 40px;
      font-size: 18px;
      color: $textSecondary;
      vertical-align: text-bottom;
      border-radius: 8px;
      margin: 0 4px;

      &.hover-effect {
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &:hover {
          background: rgba(0, 212, 255, 0.1);
          color: $techBlue;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }
      }
    }

    .avatar-container {
      margin-right: 0;

      .avatar-wrapper {
        margin-top: 0;
        position: relative;
        display: flex;
        align-items: center;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 25px;
        border: 1px solid $borderSecondary;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background: rgba(0, 212, 255, 0.1);
          border-color: $borderHover;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .user-avatar {
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid $techBlue;
          margin-right: 8px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: static;
          font-size: 12px;
          color: $textSecondary;
          transition: all 0.3s ease;
        }

        &:hover .el-icon-caret-bottom {
          color: $techBlue;
          transform: rotate(180deg);
        }
      }
    }
  }
}

// 下拉菜单样式优化
:deep(.user-dropdown) {
  background: $bgCard !important;
  backdrop-filter: blur(10px);
  border: 1px solid $borderPrimary !important;
  border-radius: 12px !important;
  box-shadow: $shadowPrimary !important;
  margin-top: 8px;

  .el-dropdown-menu__item {
    color: $textSecondary !important;
    padding: 12px 20px !important;
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    margin: 4px 8px !important;

    &:hover {
      background: rgba(0, 212, 255, 0.1) !important;
      color: $techBlue !important;
    }

    &:not(:last-child) {
      border-bottom: 1px solid $borderSecondary !important;
    }
  }
}
</style>
