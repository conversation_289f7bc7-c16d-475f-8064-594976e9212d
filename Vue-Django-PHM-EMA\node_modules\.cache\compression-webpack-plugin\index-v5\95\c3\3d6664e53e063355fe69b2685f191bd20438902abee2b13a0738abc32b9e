
cfc0ee8709d22d564da0a3d6948f305890cdde77	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"5288dcb9710f3ce864c818049197f3e3\"}","integrity":"sha512-+XMygkq5E9iHgds+FZ3lSRm3oW9eCCH4tDq+IVaClUuoE3E+OkdOd72RIYe3KICrxdHuKzy+/XjsMDdJDOdA5w==","time":1754200433139,"size":23540}