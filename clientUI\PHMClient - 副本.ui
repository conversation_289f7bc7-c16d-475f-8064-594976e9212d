<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>625</width>
    <height>648</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>机电伺服系统故障诊断和健康评估平台数据采集系统-AlwaysSun</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>20</y>
     <width>471</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>14</pointsize>
    </font>
   </property>
   <property name="text">
    <string>              机电伺服系统故障诊断和健康评估平台
                        数据采集客户端</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>580</y>
     <width>151</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>开始上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>130</y>
     <width>72</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>服务器地址：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_2">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>136</y>
     <width>131</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>10.134.104.137</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>130</y>
     <width>61</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>端口：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>390</x>
     <y>136</y>
     <width>81</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>8000</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_3">
   <property name="geometry">
    <rect>
     <x>390</x>
     <y>176</y>
     <width>81</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>1000</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>170</y>
     <width>31</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>ms</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>170</y>
     <width>51</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>上传间隔：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_5">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>230</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>225</y>
     <width>51</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>给定力：</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_9">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>224</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>N</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_16">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>82</y>
     <width>72</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>串口地址：</string>
   </property>
  </widget>
  <widget class="QComboBox" name="comboBox">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>88</y>
     <width>81</width>
     <height>22</height>
    </rect>
   </property>
   <item>
    <property name="text">
     <string>usart3</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>usart4</string>
    </property>
   </item>
  </widget>
  <widget class="QPushButton" name="pushButton_2">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>80</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>开始采集</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_2">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>230</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_17">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>224</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>N</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_18">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>225</y>
     <width>51</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>力反馈：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_9">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>230</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_3">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>230</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_10">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>270</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_19">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>264</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>mm</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_20">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>264</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>mm</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_21">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>265</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>位置给定信号：</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_4">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>270</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_11">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>270</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_22">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>265</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>实际位置信号：</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_5">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>270</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_12">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>315</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_6">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>315</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_7">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>315</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_23">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>310</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>实际速度信号：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_13">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>315</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_24">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>309</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>mm/s</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_25">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>309</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>mm/s</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_26">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>310</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>速度给定信号：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_14">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>355</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_27">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>350</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>摩擦扭矩：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_15">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>355</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_28">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>349</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>m/s2</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_8">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>355</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_29">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>349</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>Nm</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_9">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>355</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_30">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>350</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>加速度反馈：</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_10">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>395</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_31">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>390</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>A相电流：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_16">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>395</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_17">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>395</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_11">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>395</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_32">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>389</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_33">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>389</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_34">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>390</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>电流指令：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_18">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>430</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_35">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>425</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>C相电流：</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_36">
   <property name="geometry">
    <rect>
     <x>480</x>
     <y>424</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_12">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>430</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_37">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>425</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>B相电流：</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_13">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>430</y>
     <width>51</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>上传</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_38">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>424</y>
     <width>41</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>A</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_19">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>430</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_4">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>480</y>
     <width>141</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>480</y>
     <width>91</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>选择本地文件：</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_3">
   <property name="geometry">
    <rect>
     <x>300</x>
     <y>480</y>
     <width>75</width>
     <height>23</height>
    </rect>
   </property>
   <property name="text">
    <string>选择</string>
   </property>
  </widget>
  <widget class="QRadioButton" name="radioButton">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>580</y>
     <width>86</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>实时数据</string>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QRadioButton" name="radioButton_2">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>600</y>
     <width>86</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>本地文件</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
