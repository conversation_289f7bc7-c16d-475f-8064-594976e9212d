
80861b6fde16957218bd8a7e0d61286e6a5386b4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"867a6ad4148bf4c81ffb689acc3290ff\"}","integrity":"sha512-jbUqFaLn58Pt+uOeVTmmgEwXoOIMeLR4hkxXMBxecKmcIUVLURx0J87eLjMRgofUTlleUBAowzwLGpcGRxyRMA==","time":1754200376829,"size":1844898}