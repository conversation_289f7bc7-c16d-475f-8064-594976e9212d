
5f4ced30d3b1c23c11eaa46ab1a186df5a8411ec	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"8de948c819f8c9dd3aa0ffe04ae46034\"}","integrity":"sha512-y6RCUZ24817fXwKOXkOlr1U6aoMtj9PjsTmB1VfrN9kgXUUc9/Bx0gcqBoCzqm8SX7OCPwFo9EFGS7y03iBIRg==","time":1754200435799,"size":872737}