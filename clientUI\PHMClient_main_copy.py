# -*- coding: utf-8 -*-
# 20250219 此文件不完整
from PHMClient import Ui_Dialog
from PyQt5 import QtWidgets
import sys
import random,json,requests,time,datetime

from PyQt5.Qt import QThread
from PyQt5.QtCore import pyqtSignal
import os

randomFlag = True

def postData(host,port,data1,data2,x_signal,y_signal,z_signal):
    """
    修改密码    
    :param token: 
    :return: 
    """
    #targetURL = "http://**************:8000/phm/upData/"
    #targetURL = "http://127.0.0.1:8000/phm/upData/"
    targetURL = "http://"+host+":"+port+"/phm/upData/"
    print("服务器数据上传地址：",targetURL)
    postData = {'data':data1,'data2':data2,"time":str(datetime.datetime.now()),"X":x_signal,"Y":y_signal,"Z":z_signal}
    postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    req = requests.post(url=targetURL, data=postData, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    #print(req.text)

class Thread_1(QThread):  # 线程1
    _signal = pyqtSignal(str,str,str,str,str)
    def __init__(self):
        super().__init__()
        self.startFlag = False
        self.delayTime = 1000
        self.host = "127.0.0.1"
        self.port = "8000"
    def run(self):
        global randomFlag
        while self.startFlag:
            if randomFlag:
                data1 = random.randrange(100,2000)
                data2 = random.randrange(100,2000)
                x_signal = random.randrange(-30,50)
                y_signal = random.randrange(-30, 50)
                z_signal = random.randrange(-30, 50)
            else:
                data1 = 0
                data2 = 0
                x_signal = 0
                y_signal = 0
                z_signal = 0
            postData(self.host,self.port,str(data1),str(data2),str(x_signal),str(y_signal),str(z_signal))
            self._signal.emit(str(data1),str(data2),str(x_signal),str(y_signal),str(z_signal))
            #print("EMIT___",data1,data2)
            time.sleep(self.delayTime/1000.0)

class PHMClientMain(QtWidgets.QDialog,Ui_Dialog):
    def __init__(self):
        super(PHMClientMain, self).__init__()
        self.setupUi(self)
        self.flag = False
        print("__init__")
        self.pushButton_3.clicked.connect(self.getFileName)
        self.pushButton.clicked.connect(self.startSend)
        # self.threadS = Thread_1()
        # self.threadS._signal.connect(self.updateData)

    def getFileName(self):
        '''
        读取文件按键响应函数
        :return:
        '''
        print("按下读取文件按键")
        self.fileName,fileType = QtWidgets.QFileDialog.getOpenFileName(self, "选取文件", os.getcwd(),"Excel Files(*.xlsx);;All Files(*)")
        print(self.fileName,fileType)


    def startSend(self):
        '''
        开始上传按键响应函数
        :return:
        '''
        self.flag = not self.flag
        if self.flag:
            self.pushButton.setText("停止上传")
        else:
            self.pushButton.setText("开始上传")
        # self.threadS.startFlag = self.flag
        # self.threadS.host = self.lineEdit_2.text()
        # self.threadS.port = self.lineEdit.text()
        # self.threadS.delayTime = int(self.lineEdit_3.text())
        # self.threadS.start()

    def updateData(self,data1,data2,x_signal,y_signal,z_signal):
        global randomFlag
        print("上传数据：电机1转速：",data1,"rpm\t电机2转速：",data2,"rpm\tX相电流：",x_signal,"A\tY相电流：",y_signal,"A\tZ相电流：",z_signal)
        self.lineEdit_5.setText(str(data1))
        self.lineEdit_4.setText(str(data2))
        self.lineEdit_6.setText(str(x_signal))
        self.lineEdit_7.setText(str(y_signal))
        self.lineEdit_8.setText(str(z_signal))
        if self.checkBox.checkState() == 0:
            randomFlag = False
        else:
            randomFlag = True
        print(self.checkBox.checkState())



if __name__=='__main__':
    #postData()
    app = QtWidgets.QApplication(sys.argv)
    ui = PHMClientMain()
    ui.show()
    sys.exit(app.exec_())

# if __name__=="__main__":
#     import sys
#     app=QtWidgets.QApplication(sys.argv)
#     formObj=QtWidgets.QDialog()  #注意，这里和我们一开始创建窗体时使用的界面类型相同
#     ui=Ui_Dialog()
#     ui.setupUi(formObj)
#     ui.show()
#     sys.exit(app.exec_())