
d187a76bddbc8f6f2f0da3d2628403b4e0f2b059	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"72e418239e1c0c7bc0a707b8764b4757\"}","integrity":"sha512-/HVGI+c6dlnwvq0EmOUy47c8Es7wwK7JCiAIlfMpHquyrLe878AkxiRqmyO9uJClFS5YEtn56NgggsQX3FU9dA==","time":1754200396240,"size":23587}