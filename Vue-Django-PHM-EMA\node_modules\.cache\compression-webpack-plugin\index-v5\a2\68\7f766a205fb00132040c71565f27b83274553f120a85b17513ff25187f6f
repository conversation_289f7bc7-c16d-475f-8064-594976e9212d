
52758ab5d9f975f589ce936879299903887b4e6e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.49aeaa90ad6a1f802af4.hot-update.js\",\"contentHash\":\"e9b028631cc3f00378952dd50d030b74\"}","integrity":"sha512-3eg4qjlO6pJQqIoBnM6h3ppFFXJqk3CZDa+IaNVz73HtLTyzZRBBLOzhGr2cxTYClJz6XhniPl6JygfkW3hzmA==","time":1754200433139,"size":16448}