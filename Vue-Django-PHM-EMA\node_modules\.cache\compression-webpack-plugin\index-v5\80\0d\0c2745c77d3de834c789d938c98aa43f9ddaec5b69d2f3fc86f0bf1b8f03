
f78f03a49d4fa05cae53bbb2af71809f548196d7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.cfce46ca472da24db0f0.hot-update.js\",\"contentHash\":\"27b82142d0d08bbf479c104ab022275e\"}","integrity":"sha512-qXu+Wlvg1kSz1EL/RWhtrW6zmcNgnD6k1uJubyZ+tfvlDOH8/TqZxbqf2dSTQ2Yt02f+pXB1eG304eggmt1ezQ==","time":1754200376671,"size":13506}