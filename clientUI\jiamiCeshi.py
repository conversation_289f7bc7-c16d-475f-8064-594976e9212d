# -*- coding: utf-8 -*-

"""
Author  :   AlwaysSun
Time    :   2021/11/25 16:18
"""
import base64
from Crypto.Cipher import AES



def add_to_16(value):
    '''
    str不是16的倍数那就补足为16的倍数
    :param value:
    :return:
    '''
    while len(value) % 16 != 0:
        value += '\0'
    return str.encode(value)  # 返回bytes

def encrypt_oracle(key,text):
    '''
    加密某一个字符串
    :param key:加密密钥
    :param text:待加密的文本
    :return:加密后的密文
    '''
    # 初始化加密器
    aes = AES.new(add_to_16(key), AES.MODE_ECB)
    #先进行aes加密 为二进制字符
    encrypt_aes = aes.encrypt(add_to_16(text))
    #用base64转成字符串形式
    encrypted_text = str(base64.encodebytes(encrypt_aes), encoding='utf-8')  # 执行加密并转码返回bytes
    print("加密后的密文为：",encrypted_text.strip())  # 自己加的的strip， 后面会有一个换行符
    return encrypted_text.strip()

def decrypt_oralce(key,text):
    '''
    AES解密
    :param key:密钥
    :param text: 密文
    :return: 解密后的明文
    '''
    # 初始化加密器
    aes = AES.new(add_to_16(key), AES.MODE_ECB)
    #优先逆向解密base64成bytes
    base64_decrypted = base64.decodebytes(text.encode(encoding='utf-8'))
    decrypted_text = str(aes.decrypt(base64_decrypted),encoding='utf-8') # 执行解密密并转码返回str
    print("解密后的明文为：",decrypted_text)
    return decrypted_text

if __name__ == '__main__':
   decrypt_oralce('123456',encrypt_oracle('123456','yang'))