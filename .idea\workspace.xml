<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a7c06fc9-483f-4b27-8de4-e17ab9f4e48c" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/.idea/PHMSystem.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/.idea/inspectionProfiles/profiles_settings.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/.idea/misc.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/.idea/modules.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/.idea/vcs.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/.idea/workspace.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/admin.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/apps.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0001_initial.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0002_alter_user_id.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0003_auto_20210716_2119.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0004_alter_user_cookie.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0005_user_token.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0006_auto_20210716_2249.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0007_auto_20210716_2252.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/0008_user_nickname.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/migrations/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/models.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/tests.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/urls.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHM/views.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/HttpCode.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/HttpResonseMy.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/asgi.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/sendMail.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/settings.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/urls.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/PHMSystem/wsgi.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/admin.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/apps.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/migrations/0001_initial.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/migrations/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/models.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/tests.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/urls.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/User/views.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/clientPHM.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/db.sqlite3" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PHMSystem_0903/PHMSystem/manage.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
        <option value="Jupyter Notebook" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/PHMSystem_0903/PHMSystem" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2is3QrixQFgy6ZN1uxpad2wYwuP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.11.executor&quot;: &quot;Run&quot;,
    &quot;Python.PHMClient.executor&quot;: &quot;Run&quot;,
    &quot;Python.PHMClient_main.executor&quot;: &quot;Debug&quot;,
    &quot;Python.PHMClient_main2.executor&quot;: &quot;Run&quot;,
    &quot;Python.PHMClient_main_copy.executor&quot;: &quot;Run&quot;,
    &quot;Python.SelfQueue.executor&quot;: &quot;Run&quot;,
    &quot;Python.gru_train_onlyfault.executor&quot;: &quot;Run&quot;,
    &quot;Python.labviewTest.executor&quot;: &quot;Run&quot;,
    &quot;Python.test (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.test (2).executor&quot;: &quot;Run&quot;,
    &quot;Python.test.executor&quot;: &quot;Run&quot;,
    &quot;Python.test1.executor&quot;: &quot;Run&quot;,
    &quot;Python.uploadDataByQueue.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;H:/hyxd/hyxd_software/PHM/Vue-Django-PHM-EMA/src/assets&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\PyCharm 2024.1\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="H:\hyxd\hyxd_software\PHM\Vue-Django-PHM-EMA\src\assets" />
      <recent name="H:\hyxd\HYxingdong\PHM\Vue-Django-PHM-EMA\src\assets" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="H:\hyxd\HYxingdong\PHM\Vue-Django-PHM-EMA\src\assets" />
      <recent name="H:\hyxd\HYxingdong\PHM\Vue-Django-PHM-EMA\src\assets\404_images" />
    </key>
  </component>
  <component name="RunManager" selected="Python.PHMClient_main">
    <configuration name="PHMClient_main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PHM" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/clientUI" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/clientUI/PHMClient_main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="gru_train_onlyfault" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PHM" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/PHMSystem_0903/faultDiagnosis" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/PHMSystem_0903/faultDiagnosis/gru_train_onlyfault.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PHM" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/PHMSystem_0903/PHM" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/PHMSystem_0903/PHM/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test (2)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PHM" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/PHMSystem_0903/static/json" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/PHMSystem_0903/static/json/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test1" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PHM" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/PHMSystem_0903/static/allFaultData" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/PHMSystem_0903/static/allFaultData/test1.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.PHMClient_main" />
        <item itemvalue="Python.test (2)" />
        <item itemvalue="Python.test (1)" />
        <item itemvalue="Python.gru_train_onlyfault" />
        <item itemvalue="Python.test1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-PY-241.14494.241" />
        <option value="bundled-python-sdk-0509580d9d50-28c9f5db9ffe-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.14494.241" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a7c06fc9-483f-4b27-8de4-e17ab9f4e48c" name="Changes" comment="" />
      <created>1720265450043</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1720265450043</updated>
      <workItem from="1720265451374" duration="2371000" />
      <workItem from="1720327863181" duration="685000" />
      <workItem from="1720333900678" duration="717000" />
      <workItem from="1720335122573" duration="6761000" />
      <workItem from="1720689867764" duration="619000" />
      <workItem from="1739780044986" duration="8176000" />
      <workItem from="1739865886130" duration="11810000" />
      <workItem from="1739886848043" duration="359000" />
      <workItem from="1739887217229" duration="2804000" />
      <workItem from="1739929689773" duration="782000" />
      <workItem from="1739930513785" duration="2472000" />
      <workItem from="1739933132035" duration="3726000" />
      <workItem from="1739936951123" duration="12625000" />
      <workItem from="1739967806161" duration="135000" />
      <workItem from="1739968995974" duration="4977000" />
      <workItem from="1740147442206" duration="1506000" />
      <workItem from="1740285668120" duration="2801000" />
      <workItem from="1740384184873" duration="5152000" />
      <workItem from="1740538489704" duration="9905000" />
      <workItem from="1740558691870" duration="6226000" />
      <workItem from="1740661543345" duration="4690000" />
      <workItem from="1740723948337" duration="3785000" />
      <workItem from="1741088973673" duration="2507000" />
      <workItem from="1741102530610" duration="4550000" />
      <workItem from="1741683075320" duration="2180000" />
      <workItem from="1741869420337" duration="2962000" />
      <workItem from="1742108323804" duration="3009000" />
      <workItem from="1742112470456" duration="3000000" />
      <workItem from="1742210730287" duration="9220000" />
      <workItem from="1742349182430" duration="2563000" />
      <workItem from="1742351823321" duration="950000" />
      <workItem from="1742357226740" duration="245000" />
      <workItem from="1742646351452" duration="168000" />
      <workItem from="1742654070519" duration="768000" />
      <workItem from="1742656190370" duration="6497000" />
      <workItem from="1742866949762" duration="12232000" />
      <workItem from="1743068488321" duration="10360000" />
      <workItem from="1743405546748" duration="4736000" />
      <workItem from="1743425744810" duration="2922000" />
      <workItem from="1743607725975" duration="183000" />
      <workItem from="1743686755123" duration="63000" />
      <workItem from="1744212293654" duration="4788000" />
      <workItem from="1744293277132" duration="1749000" />
      <workItem from="1744345148058" duration="5395000" />
      <workItem from="1744720735260" duration="1051000" />
      <workItem from="1744722218436" duration="2024000" />
      <workItem from="1744735422408" duration="643000" />
      <workItem from="1745333672935" duration="35000" />
      <workItem from="1745375674482" duration="582000" />
      <workItem from="1745377459178" duration="12040000" />
      <workItem from="1748417095608" duration="1895000" />
      <workItem from="1749904791338" duration="34000" />
      <workItem from="1749905268054" duration="122000" />
      <workItem from="1749905519348" duration="199000" />
      <workItem from="1751617726135" duration="2364000" />
      <workItem from="1752941536367" duration="132000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/PHMSystem_0903/PHM/urls.py</url>
          <line>4</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/PHM$PHMClient_main2.coverage" NAME="PHMClient_main2 Coverage Results" MODIFIED="1739963738889" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/clientUI" />
    <SUITE FILE_PATH="coverage/PHM$test.coverage" NAME="test 覆盖结果" MODIFIED="1740545545355" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/faultDiagnosis" />
    <SUITE FILE_PATH="coverage/PHM$gru_train_onlyfault.coverage" NAME="gru_train_onlyfault 覆盖结果" MODIFIED="1740665646957" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/faultDiagnosis" />
    <SUITE FILE_PATH="coverage/PHM$uploadDataByQueue.coverage" NAME="uploadDataByQueue Coverage Results" MODIFIED="1720345638529" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/clientUI" />
    <SUITE FILE_PATH="coverage/PHM$PHMClient_main.coverage" NAME="PHMClient_main 覆盖结果" MODIFIED="1743171077569" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/clientUI" />
    <SUITE FILE_PATH="coverage/PHM$test1.coverage" NAME="test1 覆盖结果" MODIFIED="1740564736032" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/static/allFaultData" />
    <SUITE FILE_PATH="coverage/PHM$SelfQueue.coverage" NAME="SelfQueue Coverage Results" MODIFIED="1739872311708" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/PHM" />
    <SUITE FILE_PATH="coverage/PHM$PHMClient_main_copy.coverage" NAME="PHMClient_main_copy 覆盖结果" MODIFIED="1739933549006" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/clientUI" />
    <SUITE FILE_PATH="coverage/PHM$test__1_.coverage" NAME="test (1) 覆盖结果" MODIFIED="1742111137770" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/PHM" />
    <SUITE FILE_PATH="coverage/PHM$PHMClient.coverage" NAME="PHMClient Coverage Results" MODIFIED="1720345647553" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/clientUI" />
    <SUITE FILE_PATH="coverage/PHM$labviewTest.coverage" NAME="labviewTest Coverage Results" MODIFIED="1720345656521" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/clientUI" />
    <SUITE FILE_PATH="coverage/PHM$11.coverage" NAME="11 Coverage Results" MODIFIED="1739962442514" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/static/injectFaultData" />
    <SUITE FILE_PATH="coverage/PHM$test__2_.coverage" NAME="test (2) 覆盖结果" MODIFIED="1742111353119" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/PHMSystem_0903/static/json" />
  </component>
</project>