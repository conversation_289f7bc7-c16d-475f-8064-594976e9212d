{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754200394139}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,OAAA,UAAA,MAAA,cAAA;AACA,OAAA,IAAA,MAAA,MAAA;AACA,OAAA,QAAA,MAAA,YAAA,C,CACA;;AAEA,eAAA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,GAAA,EAAA,CAFA;AAGA,MAAA,IAAA,EAAA,CAHA;AAIA,MAAA,WAAA,EAAA,EAJA;AAKA,MAAA,SAAA,EAAA;AALA,KAAA;AAOA,GAVA;AAWA,EAAA,QAAA,EAAA;AACA,IAAA,YADA,0BACA;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,YAAA;AACA;AAHA,GAXA;AAgBA,EAAA,KAAA,EAAA;AACA,IAAA,MADA,oBACA;AACA,WAAA,OAAA;AACA,WAAA,gBAAA;AACA,KAJA;AAKA,IAAA,OALA,mBAKA,KALA,EAKA;AACA,UAAA,KAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,OAAA,EAAA,KAAA,SAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,KAAA,SAAA;AACA;AACA;AAXA,GAhBA;AA6BA,EAAA,OA7BA,qBA6BA;AACA,SAAA,QAAA;AACA,SAAA,OAAA;AACA,SAAA,YAAA;AACA,SAAA,gBAAA;AACA,GAlCA;AAmCA,EAAA,OAAA,EAAA;AACA,IAAA,QADA,oBACA,KADA,EACA;AACA,aAAA,KAAA,CAAA,IAAA,KAAA,KAAA,MAAA,CAAA,IAAA;AACA,KAHA;AAIA,IAAA,OAJA,mBAIA,GAJA,EAIA;AACA,aAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,KANA;AAOA,IAAA,eAPA,2BAOA,MAPA,EAOA;AAAA;;AAAA,UAAA,QAAA,uEAAA,GAAA;AACA,UAAA,IAAA,GAAA,EAAA;;AACA,UAAA,KAAA,MAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,CAAA,IAAA,IAAA,KAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA,gBAAA,OAAA,GAAA,IAAA,CAAA,OAAA,CAAA,QAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA;AACA,cAAA,QAAA,EAAA,OADA;AAEA,cAAA,IAAA,EAAA,OAFA;AAGA,cAAA,IAAA,EAAA,KAAA,CAAA,IAHA;AAIA,cAAA,IAAA,oBAAA,KAAA,CAAA,IAAA;AAJA,aAAA;AAMA;;AACA,cAAA,KAAA,CAAA,QAAA,EAAA;AACA,gBAAA,QAAA,GAAA,KAAA,CAAA,eAAA,CAAA,KAAA,CAAA,QAAA,EAAA,KAAA,CAAA,IAAA,CAAA;;AACA,gBAAA,QAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,gCAAA,IAAA,sBAAA,QAAA,EAAA;AACA;AACA;AACA,SAhBA;AAiBA;;AACA,aAAA,IAAA;AACA,KA7BA;AA8BA,IAAA,QA9BA,sBA8BA;AACA,UAAA,SAAA,GAAA,KAAA,SAAA,GAAA,KAAA,eAAA,CAAA,KAAA,MAAA,CAAA;;AADA,iDAEA,SAFA;AAAA;;AAAA;AAEA,4DAAA;AAAA,cAAA,GAAA;;AACA;AACA,cAAA,GAAA,CAAA,IAAA,EAAA;AACA,iBAAA,MAAA,CAAA,QAAA,CAAA,yBAAA,EAAA,GAAA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAQA,KAtCA;AAuCA,IAAA,OAvCA,qBAuCA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CADA,CAEA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,YAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,IAAA,KAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AAAA,OAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,GAAA,CAAA,QAAA,KAAA,KAAA,MAAA,CAAA,QAAA,EAAA;AACA,eAAA,MAAA,CAAA,QAAA,CAAA,4BAAA,EAAA,KAAA,MAAA;AACA;AACA,OALA,MAKA;AACA,aAAA,MAAA,CAAA,QAAA,CAAA,kBAAA,EAAA,KAAA,MAAA;AACA,OAdA,CAeA;;AACA,KAvDA;AAwDA,IAAA,gBAxDA,8BAwDA;AAAA;;AACA,UAAA,QAAA,CAAA,KAAA,KAAA,CAAA,eAAA,CAAA,EAAA;AACA,QAAA,SAAA,EAAA,WADA;AAEA,QAAA,SAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA,kBAAA,KAAA,EAAA;AACA,cAAA,QAAA,GAAA,KAAA,CAAA,QAAA;AAAA,cAAA,QAAA,GAAA,KAAA,CAAA,QAAA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,mBAAA,EAAA;AAAA,YAAA,QAAA,EAAA,QAAA;AAAA,YAAA,QAAA,EAAA;AAAA,WAAA;AACA;AANA,OAAA;AAQA,KAjEA;AAkEA,IAAA,gBAlEA,8BAkEA;AAAA;;AACA;AACA,WAAA,SAAA,CAAA,YAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,YAAA,CAAA,MAAA,CAAA,MAAA;AACA,OAZA;AAaA,KAjFA;AAkFA,IAAA,kBAlFA,8BAkFA,IAlFA,EAkFA;AAAA;;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,wBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,YAAA;AACA,YAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,EAAA,cAAA;AADA,WAAA;AAGA,SAJA;AAKA,OARA;AASA,KA5FA;AA6FA,IAAA,gBA7FA,4BA6FA,IA7FA,EA6FA;AAAA;;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,kBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,YAAA,YAAA,QAAA,YAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,YAAA,EAAA,IAAA;AACA;AACA,OAJA;AAKA,KAnGA;AAoGA,IAAA,eApGA,6BAoGA;AAAA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,KAAA,WAAA;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,yBAAA,EAAA,KAAA,WAAA,EAAA,IAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,gBAAA;AACA,OAFA;AAGA,KAzGA;AA0GA,IAAA,YA1GA,wBA0GA,IA1GA,EA0GA;AAAA;;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,sBAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,YAAA,YAAA,SAAA,YAAA;;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA,UAAA,GAAA;AAAA,iBAAA,GAAA,CAAA,QAAA,KAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AAAA,SAAA,CAAA,EAAA;AACA;AACA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,YAAA,EAAA,IAAA;AACA,OALA;AAMA,KAjHA;AAkHA,IAAA,UAlHA,sBAkHA,YAlHA,EAkHA,IAlHA,EAkHA;AACA,UAAA,UAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;AACA,UAAA,UAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,UAAA,CAAA,QAAA;AACA,OAFA,MAEA;AACA;AACA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,KAAA,EAAA;AACA;AACA,eAAA,OAAA,CAAA,OAAA,CAAA;AAAA,YAAA,IAAA,EAAA,cAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAHA,MAGA;AACA,eAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA;AACA,KAhIA;AAiIA,IAAA,QAjIA,oBAiIA,GAjIA,EAiIA,CAjIA,EAiIA;AACA,UAAA,YAAA,GAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,GAAA,CAAA,qBAAA,GAAA,IAAA,CAFA,CAEA;;AACA,UAAA,WAAA,GAAA,KAAA,GAAA,CAAA,WAAA,CAHA,CAGA;;AACA,UAAA,OAAA,GAAA,WAAA,GAAA,YAAA,CAJA,CAIA;;AACA,UAAA,IAAA,GAAA,CAAA,CAAA,OAAA,GAAA,UAAA,GAAA,EAAA,CALA,CAKA;;AAEA,UAAA,IAAA,GAAA,OAAA,EAAA;AACA,aAAA,IAAA,GAAA,OAAA;AACA,OAFA,MAEA;AACA,aAAA,IAAA,GAAA,IAAA;AACA;;AAEA,WAAA,GAAA,GAAA,CAAA,CAAA,OAAA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,KAjJA;AAkJA,IAAA,SAlJA,uBAkJA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,KApJA;AAqJA,IAAA,YArJA,0BAqJA;AACA,WAAA,SAAA;AACA,KAvJA;AAwJA,IAAA,YAxJA,0BAwJA;AAAA;;AACA;AACA,MAAA,MAAA,CAAA,gBAAA,CAAA,cAAA,EAAA,YAAA;AACA;AACA,YAAA,QAAA,GAAA,MAAA,CAAA,YAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,iBAAA;AACA,YAAA,QAAA,EAAA,IAAA,CAAA,QADA;AAEA,YAAA,IAAA,EAAA,IAAA,CAAA,IAFA;AAGA,YAAA,IAAA,oBAAA,IAAA,CAAA,IAAA,CAHA;AAIA,YAAA,IAAA,EAAA,IAAA,CAAA,IAJA;AAKA,YAAA,MAAA,oBAAA,IAAA,CAAA,MAAA,CALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA,IANA;AAOA,YAAA,KAAA,oBAAA,IAAA,CAAA,KAAA,CAPA;AAQA,YAAA,KAAA,EAAA,IAAA,CAAA;AARA,WAAA;AAUA,SAXA,CAAA;;AAYA,QAAA,cAAA,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA;AACA,OAfA,EAFA,CAkBA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,CAAA,KAAA,EAAA;;AACA,UAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,YAAA,GAAA,QAAA;AACA;AACA;AA/KA;AAnCA,CAAA", "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <!-- <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link> -->\n      <div ref=\"sortable-wrap\">\n        <router-link\n          v-for=\"tag in visitedViews\"\n          :key=\"tag.path\"\n          :class=\"{ active: isActive(tag), sortable: !isAffix(tag) }\"\n          :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n          tag=\"span\"\n          class=\"tags-view-item\"\n          :data-full-path=\"tag.fullPath\"\n          @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n          @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n        >\n          {{ tag.title }}\n          <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n        </router-link>\n      </div>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\">刷新</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\">关闭</li>\n      <li @click=\"closeOthersTags\">关闭其他标签</li>\n      <li @click=\"closeAllTags(selectedTag)\">关闭所有标签</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\nimport Sortable from 'sortablejs'\n// import { mapState } from 'vuex'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n    this.beforeUnload()\n    this.initSortableTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      if (this.routes) {\n        routes.forEach(route => {\n          if (route.meta && route.meta.affix) {\n            const tagPath = path.resolve(basePath, route.path)\n            tags.push({\n              fullPath: tagPath,\n              path: tagPath,\n              name: route.name,\n              meta: { ...route.meta }\n            })\n          }\n          if (route.children) {\n            const tempTags = this.filterAffixTags(route.children, route.path)\n            if (tempTags.length >= 1) {\n              tags = [...tags, ...tempTags]\n            }\n          }\n        })\n      }\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      // if (name) {\n      if (!name) {\n        return\n      }\n      const res = this.visitedViews.find(v => v.path === this.$route.path)\n      if (res) {\n        // when query is different then update\n        if (res.fullPath !== this.$route.fullPath) {\n          this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n        }\n      } else {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n      // return false\n    },\n    initSortableTags() {\n      new Sortable(this.$refs['sortable-wrap'], {\n        draggable: '.sortable',\n        animation: 200,\n        onUpdate: event => {\n          const { oldIndex, newIndex } = event\n          this.$store.dispatch('tagsView/moveView', { oldIndex, newIndex })\n        }\n      })\n    },\n    moveToCurrentTag() {\n      // const tags = this.$refs.tag\n      this.$nextTick(() => {\n        // for (const tag of tags) {\n        //   if (tag.to.path === this.$route.path) {\n        //     this.$refs.scrollPane.moveToTarget(tag)\n        //     // when query is different then update\n        //     if (tag.to.fullPath !== this.$route.fullPath) {\n        //       this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n        //     }\n        //     break\n        //   }\n        // }\n        this.$refs.scrollPane.moveToTarget(this.$route)\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\n        const { fullPath } = view\n        console.log('单页面刷新', view)\n        this.$nextTick(() => {\n          this.$router.replace({\n            path: '/redirect' + fullPath\n          })\n        })\n      })\n    },\n    closeSelectedTag(view) {\n      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag)\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.fullPath === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === '主控台') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    },\n    beforeUnload() {\n      // 监听页面刷新\n      window.addEventListener('beforeunload', () => {\n        // visitedViews数据结构太复杂无法直接JSON.stringify处理，先转换需要的数据\n        const tabViews = this.visitedViews.map(item => {\n          return {\n            fullPath: item.fullPath,\n            hash: item.hash,\n            meta: { ...item.meta },\n            name: item.name,\n            params: { ...item.params },\n            path: item.path,\n            query: { ...item.query },\n            title: item.title\n          }\n        })\n        sessionStorage.setItem('tabViews', JSON.stringify(tabViews))\n      })\n      // 页面初始化加载判断缓存中是否有数据\n      const oldViews = JSON.parse(sessionStorage.getItem('tabViews')) || []\n      if (oldViews.length > 0) {\n        this.$store.state.tagsView.visitedViews = oldViews\n      }\n    }\n\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.tags-view-container {\n  height: 40px;\n  width: 100%;\n  background: linear-gradient(135deg, $bgSecondary 0%, $bgPrimary 100%);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid $borderPrimary;\n  box-shadow: $shadowPrimary;\n\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 28px;\n      line-height: 28px;\n      border: 1px solid $borderSecondary;\n      color: $textSecondary;\n      background: rgba(255, 255, 255, 0.05);\n      padding: 0 12px;\n      font-size: 13px;\n      margin-left: 6px;\n      margin-top: 6px;\n      border-radius: 6px;\n      transition: all 0.3s ease;\n      backdrop-filter: blur(5px);\n\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n\n      &:hover {\n        background: rgba(0, 212, 255, 0.1);\n        border-color: $borderHover;\n        color: $techBlue;\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);\n      }\n\n      &.active {\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        color: $textPrimary;\n        border-color: $techBlue;\n        box-shadow: 0 2px 12px rgba(0, 212, 255, 0.4);\n        font-weight: 500;\n\n        &::before {\n          content: '';\n          background: $textPrimary;\n          display: inline-block;\n          width: 6px;\n          height: 6px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 6px;\n          box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);\n        }\n      }\n    }\n  }\n\n  .contextmenu {\n    margin: 0;\n    background: $bgCard;\n    backdrop-filter: blur(10px);\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 8px 0;\n    border-radius: 8px;\n    font-size: 13px;\n    font-weight: 400;\n    color: $textSecondary;\n    border: 1px solid $borderPrimary;\n    box-shadow: $shadowPrimary;\n\n    li {\n      margin: 0;\n      padding: 10px 16px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border-radius: 6px;\n      margin: 2px 6px;\n\n      &:hover {\n        background: rgba(0, 212, 255, 0.1);\n        color: $techBlue;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n@import \"~@/styles/variables.scss\";\n\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n      transform-origin: 100% 50%;\n      margin-left: 4px;\n\n      &:before {\n        transform: scale(0.7);\n        display: inline-block;\n        vertical-align: -2px;\n      }\n\n      &:hover {\n        background-color: rgba(255, 77, 87, 0.8);\n        color: $textPrimary;\n        transform: scale(1.1);\n        box-shadow: 0 2px 6px rgba(255, 77, 87, 0.3);\n      }\n    }\n\n    &.active .el-icon-close {\n      color: $textPrimary;\n\n      &:hover {\n        background-color: rgba(255, 255, 255, 0.2);\n        color: $textPrimary;\n      }\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/layout/components/TagsView"}]}