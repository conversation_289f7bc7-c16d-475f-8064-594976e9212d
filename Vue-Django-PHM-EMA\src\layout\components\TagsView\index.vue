<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPane" class="tags-view-wrapper" @scroll="handleScroll">
      <!-- <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="isActive(tag)?'active':''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        tag="span"
        class="tags-view-item"
        @click.middle.native="!isAffix(tag)?closeSelectedTag(tag):''"
        @contextmenu.prevent.native="openMenu(tag,$event)"
      >
        {{ tag.title }}
        <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
      </router-link> -->
      <div ref="sortable-wrap">
        <router-link
          v-for="tag in visitedViews"
          :key="tag.path"
          :class="{ active: isActive(tag), sortable: !isAffix(tag) }"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
          tag="span"
          class="tags-view-item"
          :data-full-path="tag.fullPath"
          @click.middle.native="!isAffix(tag)?closeSelectedTag(tag):''"
          @contextmenu.prevent.native="openMenu(tag,$event)"
        >
          {{ tag.title }}
          <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
        </router-link>
      </div>
    </scroll-pane>
    <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">刷新</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">关闭</li>
      <li @click="closeOthersTags">关闭其他标签</li>
      <li @click="closeAllTags(selectedTag)">关闭所有标签</li>
    </ul>
  </div>
</template>

<script>
import ScrollPane from './ScrollPane'
import path from 'path'
import Sortable from 'sortablejs'
// import { mapState } from 'vuex'

export default {
  components: { ScrollPane },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: []
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
    this.beforeUnload()
    this.initSortableTags()
  },
  methods: {
    isActive(route) {
      return route.path === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      if (this.routes) {
        routes.forEach(route => {
          if (route.meta && route.meta.affix) {
            const tagPath = path.resolve(basePath, route.path)
            tags.push({
              fullPath: tagPath,
              path: tagPath,
              name: route.name,
              meta: { ...route.meta }
            })
          }
          if (route.children) {
            const tempTags = this.filterAffixTags(route.children, route.path)
            if (tempTags.length >= 1) {
              tags = [...tags, ...tempTags]
            }
          }
        })
      }
      return tags
    },
    initTags() {
      const affixTags = this.affixTags = this.filterAffixTags(this.routes)
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      // if (name) {
      if (!name) {
        return
      }
      const res = this.visitedViews.find(v => v.path === this.$route.path)
      if (res) {
        // when query is different then update
        if (res.fullPath !== this.$route.fullPath) {
          this.$store.dispatch('tagsView/updateVisitedView', this.$route)
        }
      } else {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      // return false
    },
    initSortableTags() {
      new Sortable(this.$refs['sortable-wrap'], {
        draggable: '.sortable',
        animation: 200,
        onUpdate: event => {
          const { oldIndex, newIndex } = event
          this.$store.dispatch('tagsView/moveView', { oldIndex, newIndex })
        }
      })
    },
    moveToCurrentTag() {
      // const tags = this.$refs.tag
      this.$nextTick(() => {
        // for (const tag of tags) {
        //   if (tag.to.path === this.$route.path) {
        //     this.$refs.scrollPane.moveToTarget(tag)
        //     // when query is different then update
        //     if (tag.to.fullPath !== this.$route.fullPath) {
        //       this.$store.dispatch('tagsView/updateVisitedView', this.$route)
        //     }
        //     break
        //   }
        // }
        this.$refs.scrollPane.moveToTarget(this.$route)
      })
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        console.log('单页面刷新', view)
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some(tag => tag.fullPath === this.$route.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === '主控台') {
          // to reload home page
          this.$router.replace({ path: '/redirect' + view.fullPath })
        } else {
          this.$router.push('/')
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    beforeUnload() {
      // 监听页面刷新
      window.addEventListener('beforeunload', () => {
        // visitedViews数据结构太复杂无法直接JSON.stringify处理，先转换需要的数据
        const tabViews = this.visitedViews.map(item => {
          return {
            fullPath: item.fullPath,
            hash: item.hash,
            meta: { ...item.meta },
            name: item.name,
            params: { ...item.params },
            path: item.path,
            query: { ...item.query },
            title: item.title
          }
        })
        sessionStorage.setItem('tabViews', JSON.stringify(tabViews))
      })
      // 页面初始化加载判断缓存中是否有数据
      const oldViews = JSON.parse(sessionStorage.getItem('tabViews')) || []
      if (oldViews.length > 0) {
        this.$store.state.tagsView.visitedViews = oldViews
      }
    }

  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.tags-view-container {
  height: 40px;
  width: 100%;
  background: linear-gradient(135deg, $bgSecondary 0%, $bgPrimary 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid $borderPrimary;
  box-shadow: $shadowPrimary;

  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 28px;
      line-height: 28px;
      border: 1px solid $borderSecondary;
      color: $textSecondary;
      background: rgba(255, 255, 255, 0.05);
      padding: 0 12px;
      font-size: 13px;
      margin-left: 6px;
      margin-top: 6px;
      border-radius: 6px;
      transition: all 0.3s ease;
      backdrop-filter: blur(5px);

      &:first-of-type {
        margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }

      &:hover {
        background: rgba(0, 212, 255, 0.1);
        border-color: $borderHover;
        color: $techBlue;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
      }

      &.active {
        background: linear-gradient(135deg, $techBlue, $techBlueLight);
        color: $textPrimary;
        border-color: $techBlue;
        box-shadow: 0 2px 12px rgba(0, 212, 255, 0.4);
        font-weight: 500;

        &::before {
          content: '';
          background: $textPrimary;
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          position: relative;
          margin-right: 6px;
          box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: $bgCard;
    backdrop-filter: blur(10px);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 8px 0;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 400;
    color: $textSecondary;
    border: 1px solid $borderPrimary;
    box-shadow: $shadowPrimary;

    li {
      margin: 0;
      padding: 10px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 6px;
      margin: 2px 6px;

      &:hover {
        background: rgba(0, 212, 255, 0.1);
        color: $techBlue;
      }
    }
  }
}
</style>

<style lang="scss">
@import "~@/styles/variables.scss";

//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      transform-origin: 100% 50%;
      margin-left: 4px;

      &:before {
        transform: scale(0.7);
        display: inline-block;
        vertical-align: -2px;
      }

      &:hover {
        background-color: rgba(255, 77, 87, 0.8);
        color: $textPrimary;
        transform: scale(1.1);
        box-shadow: 0 2px 6px rgba(255, 77, 87, 0.3);
      }
    }

    &.active .el-icon-close {
      color: $textPrimary;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: $textPrimary;
      }
    }
  }
}
</style>
