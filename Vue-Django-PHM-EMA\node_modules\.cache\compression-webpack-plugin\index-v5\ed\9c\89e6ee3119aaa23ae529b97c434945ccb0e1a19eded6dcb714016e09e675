
92f430933d63537927562462a606ee15ce58a5ac	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.cfce46ca472da24db0f0.hot-update.js\",\"contentHash\":\"27b82142d0d08bbf479c104ab022275e\"}","integrity":"sha512-SraPR1TToe5fYiad0Nh14YQUEifMhym7rwkYH8Nrmvn/3V6IiO6BsnU/siPW85BOjyosr15O0ISPCC9y0xyaxA==","time":1754200377149,"size":12247}