
21d93b1329895d16bd38f42dd496e2c9386563e7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"c0cd28b05a255512d93d2d497d2c8f0a\"}","integrity":"sha512-PCV8ee25xfR6p6uDLN4dhCbQIQ5qdeED4dif2K2peKECdKyZTmGetKo5gC7jSVNelcO//b1kSzD93h5JTiYD+w==","time":1754200377150,"size":23477}