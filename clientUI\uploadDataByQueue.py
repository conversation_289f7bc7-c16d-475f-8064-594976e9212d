# -*- coding: utf-8 -*-

"""
Author  :   AlwaysSun
Time    :   2021/11/22 9:47
主要为了实现无数据丢失的客户端数据上传
"""
import random,datetime,xlrd,json,requests
import time
import threading

# 保存所有数据的队列
allDataQueue = []

class SendThread(threading.Thread):
    def __init__(self,threadName,targetURL):
        '''
        上传数据线程 从全局队列allDataQueue中依此读取数据
        :param threadName: 线程名称
        :param targetURL: 远程URL
        '''
        threading.Thread.__init__(self)
        self.name = threadName
        self.targetURL = targetURL
        self.countNum = 0

    def readOneDataFromQueue(self) -> dict:
        '''
        从数据队列中读取头数据，如果长度为0 必须等待之后在发送
        因此向队列中放数据和读数据必须在两个线程中，不然会一直在读数据的while循环中
        :return: 读取的头数据
        '''
        global allDataQueue
        # 列表为空 不能进行发送 需要等待数据读取
        while len(allDataQueue) == 0:
            time.sleep(0.0001)
        print("队列剩余长度",len(allDataQueue)-1,end='\t')
        return allDataQueue.pop(0)

    def uploadOneData(self,postDataSource: dict) -> None:
        '''
        输入一条数据，将数据上传至服务器
        :param postDataSource:单条数据
        :return:无
        '''
        #print("开始上传数据",postDataSource)
        postData = json.dumps(postDataSource)
        myHeader = {"user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
        req = requests.post(url=self.targetURL, data=str(postData), headers=myHeader)
        code = eval(req.text)
        if code['code'] == 200:
            self.countNum += 1
            print("上传计数",self.countNum)
            print('成功发送数据---', postData, '---返回值为---', code['controlData'])

    def run(self):
        print("++++++上传线程开始工作++++++")
        while True:
            self.uploadOneData(self.readOneDataFromQueue())
            time.sleep(0.2)

class ReadDataThread(threading.Thread):
    def __init__(self,threadName):
        '''
        读取数据线程 模拟数据读取过程 读取之后保存在全局变量 allDataQueue 中
        :param threadName: 线程名称
        '''
        threading.Thread.__init__(self)
        self.name = threadName
        # 读取的数据个数计数
        self.countNum  = 0

    def read_excel(self,xlsxName: str) -> tuple:
        '''
        读取一个xlxs文件
        :param xlsxName: 文件名
        :return:读取的数据列表，依此为给定位移，实际位移，负载力，Iq
        '''
        workbook = xlrd.open_workbook(xlsxName)
        sheet1_name = workbook.sheet_names()[0]
        sheet1 = workbook.sheet_by_name(sheet1_name)
        xGive = sheet1.col_values(9)[1:]
        xGet = sheet1.col_values(11)[1:]
        fGet = sheet1.col_values(3)[1:]
        IqGet = sheet1.col_values(13)[1:]
        return xGive, xGet, fGet,IqGet

    def putOneDataInQueue(self,data: dict) -> None:
        '''
        向数据队列中加入一条数据，该函数和readOneDataFromQueue()必须处于两个线程中运行
        :param data: 需要放入的数据
        :return: 无
        '''
        global allDataQueue
        allDataQueue.append(data)
        self.countNum += 1
        print("写入计数",self.countNum)

    def sendOneFile(self,fileName):
        '''
        发送一个xlxs文件
        :param fileName:文件名
        :return:
        '''
        self.xGive, self.xGet, self.fGet, self.IqGet = self.read_excel(fileName)
        print("文件长度为---",len(self.xGive))
        for i in range(len(self.xGive)):
            postData = {"time": str(datetime.datetime.now()), "type": '1', "Iq": str(self.IqGet[i]),'x_give': str(self.xGive[i]), 'x_get': str(self.xGet[i]), 'f_get': str(self.fGet[i])}
            # 单条数据读取时间控制
            time.sleep(0.5)
            self.putOneDataInQueue(postData)

    def run(self):
        print("++++++读数据线程开始工作++++++")
        while True:
            self.sendOneFile(r'2-0.1-0-25-0.0005.xlsx')
            break



if __name__ == '__main__':
    # updataURL = "http://101.42.93.111:8000/phm/upData/"
    updataURL = "http://127.0.0.1:8000/phm/upData/"
    readDataThread = ReadDataThread('Read data and save to queue')
    readDataThread.start()
    runThread = SendThread('Read from queue and send it to server',updataURL)
    runThread.start()
    runThread.join()
    readDataThread.join()