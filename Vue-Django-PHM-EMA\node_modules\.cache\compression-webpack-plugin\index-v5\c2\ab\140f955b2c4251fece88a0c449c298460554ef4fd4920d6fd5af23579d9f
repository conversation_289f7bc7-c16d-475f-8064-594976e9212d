
2f467c6ba0427e5b5d2884fc76c35d87e1ccaad4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.49aeaa90ad6a1f802af4.hot-update.js\",\"contentHash\":\"e9b028631cc3f00378952dd50d030b74\"}","integrity":"sha512-WzVu2Hptg9sT7szJ3oeQnRoV1VKmaXTZv9OB6aQb64eqfHf+nH4dAZO4TP2Hr00WxG2uK+ZZqim8UgnaNiKe6A==","time":1754200432696,"size":18241}