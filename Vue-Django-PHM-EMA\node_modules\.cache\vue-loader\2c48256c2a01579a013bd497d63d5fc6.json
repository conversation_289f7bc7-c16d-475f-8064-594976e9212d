{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754200394139}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <!-- <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"isActive(tag)?'active':''\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link> -->\n      <div ref=\"sortable-wrap\">\n        <router-link\n          v-for=\"tag in visitedViews\"\n          :key=\"tag.path\"\n          :class=\"{ active: isActive(tag), sortable: !isAffix(tag) }\"\n          :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n          tag=\"span\"\n          class=\"tags-view-item\"\n          :data-full-path=\"tag.fullPath\"\n          @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n          @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n        >\n          {{ tag.title }}\n          <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n        </router-link>\n      </div>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\">刷新</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\">关闭</li>\n      <li @click=\"closeOthersTags\">关闭其他标签</li>\n      <li @click=\"closeAllTags(selectedTag)\">关闭所有标签</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\nimport Sortable from 'sortablejs'\n// import { mapState } from 'vuex'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n    this.beforeUnload()\n    this.initSortableTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      if (this.routes) {\n        routes.forEach(route => {\n          if (route.meta && route.meta.affix) {\n            const tagPath = path.resolve(basePath, route.path)\n            tags.push({\n              fullPath: tagPath,\n              path: tagPath,\n              name: route.name,\n              meta: { ...route.meta }\n            })\n          }\n          if (route.children) {\n            const tempTags = this.filterAffixTags(route.children, route.path)\n            if (tempTags.length >= 1) {\n              tags = [...tags, ...tempTags]\n            }\n          }\n        })\n      }\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      // if (name) {\n      if (!name) {\n        return\n      }\n      const res = this.visitedViews.find(v => v.path === this.$route.path)\n      if (res) {\n        // when query is different then update\n        if (res.fullPath !== this.$route.fullPath) {\n          this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n        }\n      } else {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n      // return false\n    },\n    initSortableTags() {\n      new Sortable(this.$refs['sortable-wrap'], {\n        draggable: '.sortable',\n        animation: 200,\n        onUpdate: event => {\n          const { oldIndex, newIndex } = event\n          this.$store.dispatch('tagsView/moveView', { oldIndex, newIndex })\n        }\n      })\n    },\n    moveToCurrentTag() {\n      // const tags = this.$refs.tag\n      this.$nextTick(() => {\n        // for (const tag of tags) {\n        //   if (tag.to.path === this.$route.path) {\n        //     this.$refs.scrollPane.moveToTarget(tag)\n        //     // when query is different then update\n        //     if (tag.to.fullPath !== this.$route.fullPath) {\n        //       this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n        //     }\n        //     break\n        //   }\n        // }\n        this.$refs.scrollPane.moveToTarget(this.$route)\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\n        const { fullPath } = view\n        console.log('单页面刷新', view)\n        this.$nextTick(() => {\n          this.$router.replace({\n            path: '/redirect' + fullPath\n          })\n        })\n      })\n    },\n    closeSelectedTag(view) {\n      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag)\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.fullPath === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === '主控台') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    },\n    beforeUnload() {\n      // 监听页面刷新\n      window.addEventListener('beforeunload', () => {\n        // visitedViews数据结构太复杂无法直接JSON.stringify处理，先转换需要的数据\n        const tabViews = this.visitedViews.map(item => {\n          return {\n            fullPath: item.fullPath,\n            hash: item.hash,\n            meta: { ...item.meta },\n            name: item.name,\n            params: { ...item.params },\n            path: item.path,\n            query: { ...item.query },\n            title: item.title\n          }\n        })\n        sessionStorage.setItem('tabViews', JSON.stringify(tabViews))\n      })\n      // 页面初始化加载判断缓存中是否有数据\n      const oldViews = JSON.parse(sessionStorage.getItem('tabViews')) || []\n      if (oldViews.length > 0) {\n        this.$store.state.tagsView.visitedViews = oldViews\n      }\n    }\n\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.tags-view-container {\n  height: 40px;\n  width: 100%;\n  background: linear-gradient(135deg, $bgSecondary 0%, $bgPrimary 100%);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid $borderPrimary;\n  box-shadow: $shadowPrimary;\n\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 28px;\n      line-height: 28px;\n      border: 1px solid $borderSecondary;\n      color: $textSecondary;\n      background: rgba(255, 255, 255, 0.05);\n      padding: 0 12px;\n      font-size: 13px;\n      margin-left: 6px;\n      margin-top: 6px;\n      border-radius: 6px;\n      transition: all 0.3s ease;\n      backdrop-filter: blur(5px);\n\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n\n      &:hover {\n        background: rgba(0, 212, 255, 0.1);\n        border-color: $borderHover;\n        color: $techBlue;\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);\n      }\n\n      &.active {\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        color: $textPrimary;\n        border-color: $techBlue;\n        box-shadow: 0 2px 12px rgba(0, 212, 255, 0.4);\n        font-weight: 500;\n\n        &::before {\n          content: '';\n          background: $textPrimary;\n          display: inline-block;\n          width: 6px;\n          height: 6px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 6px;\n          box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);\n        }\n      }\n    }\n  }\n\n  .contextmenu {\n    margin: 0;\n    background: $bgCard;\n    backdrop-filter: blur(10px);\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 8px 0;\n    border-radius: 8px;\n    font-size: 13px;\n    font-weight: 400;\n    color: $textSecondary;\n    border: 1px solid $borderPrimary;\n    box-shadow: $shadowPrimary;\n\n    li {\n      margin: 0;\n      padding: 10px 16px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border-radius: 6px;\n      margin: 2px 6px;\n\n      &:hover {\n        background: rgba(0, 212, 255, 0.1);\n        color: $techBlue;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n@import \"~@/styles/variables.scss\";\n\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n      transform-origin: 100% 50%;\n      margin-left: 4px;\n\n      &:before {\n        transform: scale(0.7);\n        display: inline-block;\n        vertical-align: -2px;\n      }\n\n      &:hover {\n        background-color: rgba(255, 77, 87, 0.8);\n        color: $textPrimary;\n        transform: scale(1.1);\n        box-shadow: 0 2px 6px rgba(255, 77, 87, 0.3);\n      }\n    }\n\n    &.active .el-icon-close {\n      color: $textPrimary;\n\n      &:hover {\n        background-color: rgba(255, 255, 255, 0.2);\n        color: $textPrimary;\n      }\n    }\n  }\n}\n</style>\n"]}]}