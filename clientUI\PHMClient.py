# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'PHMClient.ui'
#
# Created by: PyQt5 UI code generator 5.15.2
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.resize(703, 728)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("buaa.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        Dialog.setWindowIcon(icon)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(Dialog)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.label_32 = QtWidgets.QLabel(Dialog)
        self.label_32.setEnabled(True)
        self.label_32.setText("")
        self.label_32.setPixmap(QtGui.QPixmap("buaa_s.png"))
        self.label_32.setObjectName("label_32")
        self.horizontalLayout_9.addWidget(self.label_32)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem)
        self.label = QtWidgets.QLabel(Dialog)
        font = QtGui.QFont()
        font.setPointSize(14)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.horizontalLayout_9.addWidget(self.label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem1)
        self.horizontalLayout_9.setStretch(0, 1)
        self.horizontalLayout_9.setStretch(2, 3)
        self.horizontalLayout_9.setStretch(3, 1)
        self.verticalLayout_2.addLayout(self.horizontalLayout_9)
        self.line_2 = QtWidgets.QFrame(Dialog)
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_2.setObjectName("line_2")
        self.verticalLayout_2.addWidget(self.line_2)
        self.label_10 = QtWidgets.QLabel(Dialog)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_10.setFont(font)
        self.label_10.setObjectName("label_10")
        self.verticalLayout_2.addWidget(self.label_10)
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.label_16 = QtWidgets.QLabel(Dialog)
        self.label_16.setObjectName("label_16")
        self.horizontalLayout_22.addWidget(self.label_16)
        self.comboBox = QtWidgets.QComboBox(Dialog)
        self.comboBox.setObjectName("comboBox")
        self.comboBox.addItem("")
        self.comboBox.addItem("")
        self.horizontalLayout_22.addWidget(self.comboBox)
        self.horizontalLayout_23.addLayout(self.horizontalLayout_22)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_23.addItem(spacerItem2)
        self.pushButton_2 = QtWidgets.QPushButton(Dialog)
        self.pushButton_2.setObjectName("pushButton_2")
        self.horizontalLayout_23.addWidget(self.pushButton_2)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_23.addItem(spacerItem3)
        self.checkBox = QtWidgets.QCheckBox(Dialog)
        self.checkBox.setChecked(True)
        self.checkBox.setObjectName("checkBox")
        self.horizontalLayout_23.addWidget(self.checkBox)
        self.horizontalLayout_23.setStretch(0, 2)
        self.horizontalLayout_23.setStretch(2, 2)
        self.verticalLayout_2.addLayout(self.horizontalLayout_23)
        self.line = QtWidgets.QFrame(Dialog)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.verticalLayout_2.addWidget(self.line)
        self.label_8 = QtWidgets.QLabel(Dialog)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_8.setFont(font)
        self.label_8.setObjectName("label_8")
        self.verticalLayout_2.addWidget(self.label_8)
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setSpacing(15)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label_6 = QtWidgets.QLabel(Dialog)
        self.label_6.setObjectName("label_6")
        self.horizontalLayout.addWidget(self.label_6)
        self.lineEdit_Iq = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_Iq.setObjectName("lineEdit_Iq")
        self.horizontalLayout.addWidget(self.lineEdit_Iq)
        self.label_9 = QtWidgets.QLabel(Dialog)
        self.label_9.setObjectName("label_9")
        self.horizontalLayout.addWidget(self.label_9)
        self.checkBox_2 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_2.setChecked(True)
        self.checkBox_2.setObjectName("checkBox_2")
        self.horizontalLayout.addWidget(self.checkBox_2)
        self.horizontalLayout_13.addLayout(self.horizontalLayout)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_18 = QtWidgets.QLabel(Dialog)
        self.label_18.setObjectName("label_18")
        self.horizontalLayout_2.addWidget(self.label_18)
        self.lineEdit_fget = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_fget.setObjectName("lineEdit_fget")
        self.horizontalLayout_2.addWidget(self.lineEdit_fget)
        self.label_17 = QtWidgets.QLabel(Dialog)
        self.label_17.setObjectName("label_17")
        self.horizontalLayout_2.addWidget(self.label_17)
        self.checkBox_3 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_3.setEnabled(True)
        self.checkBox_3.setCheckable(True)
        self.checkBox_3.setChecked(True)
        self.checkBox_3.setObjectName("checkBox_3")
        self.horizontalLayout_2.addWidget(self.checkBox_3)
        self.horizontalLayout_13.addLayout(self.horizontalLayout_2)
        self.verticalLayout.addLayout(self.horizontalLayout_13)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.label_21 = QtWidgets.QLabel(Dialog)
        self.label_21.setObjectName("label_21")
        self.horizontalLayout_3.addWidget(self.label_21)
        self.lineEdit_xgive = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_xgive.setObjectName("lineEdit_xgive")
        self.horizontalLayout_3.addWidget(self.lineEdit_xgive)
        self.label_20 = QtWidgets.QLabel(Dialog)
        self.label_20.setObjectName("label_20")
        self.horizontalLayout_3.addWidget(self.label_20)
        self.checkBox_5 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_5.setChecked(True)
        self.checkBox_5.setObjectName("checkBox_5")
        self.horizontalLayout_3.addWidget(self.checkBox_5)
        self.horizontalLayout_14.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_22 = QtWidgets.QLabel(Dialog)
        self.label_22.setObjectName("label_22")
        self.horizontalLayout_4.addWidget(self.label_22)
        self.lineEdit_xget = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_xget.setObjectName("lineEdit_xget")
        self.horizontalLayout_4.addWidget(self.lineEdit_xget)
        self.label_19 = QtWidgets.QLabel(Dialog)
        self.label_19.setObjectName("label_19")
        self.horizontalLayout_4.addWidget(self.label_19)
        self.checkBox_4 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_4.setChecked(True)
        self.checkBox_4.setObjectName("checkBox_4")
        self.horizontalLayout_4.addWidget(self.checkBox_4)
        self.horizontalLayout_14.addLayout(self.horizontalLayout_4)
        self.verticalLayout.addLayout(self.horizontalLayout_14)
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.label_26 = QtWidgets.QLabel(Dialog)
        self.label_26.setObjectName("label_26")
        self.horizontalLayout_5.addWidget(self.label_26)
        self.lineEdit_vgive = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_vgive.setObjectName("lineEdit_vgive")
        self.horizontalLayout_5.addWidget(self.lineEdit_vgive)
        self.label_25 = QtWidgets.QLabel(Dialog)
        self.label_25.setObjectName("label_25")
        self.horizontalLayout_5.addWidget(self.label_25)
        self.checkBox_6 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_6.setObjectName("checkBox_6")
        self.horizontalLayout_5.addWidget(self.checkBox_6)
        self.horizontalLayout_15.addLayout(self.horizontalLayout_5)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label_23 = QtWidgets.QLabel(Dialog)
        self.label_23.setObjectName("label_23")
        self.horizontalLayout_6.addWidget(self.label_23)
        self.lineEdit_vget = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_vget.setObjectName("lineEdit_vget")
        self.horizontalLayout_6.addWidget(self.lineEdit_vget)
        self.label_24 = QtWidgets.QLabel(Dialog)
        self.label_24.setObjectName("label_24")
        self.horizontalLayout_6.addWidget(self.label_24)
        self.checkBox_7 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_7.setObjectName("checkBox_7")
        self.horizontalLayout_6.addWidget(self.checkBox_7)
        self.horizontalLayout_15.addLayout(self.horizontalLayout_6)
        self.verticalLayout.addLayout(self.horizontalLayout_15)
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.label_27 = QtWidgets.QLabel(Dialog)
        self.label_27.setObjectName("label_27")
        self.horizontalLayout_7.addWidget(self.label_27)
        self.lineEdit_mocaget = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_mocaget.setObjectName("lineEdit_mocaget")
        self.horizontalLayout_7.addWidget(self.lineEdit_mocaget)
        self.label_29 = QtWidgets.QLabel(Dialog)
        self.label_29.setObjectName("label_29")
        self.horizontalLayout_7.addWidget(self.label_29)
        self.checkBox_9 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_9.setObjectName("checkBox_9")
        self.horizontalLayout_7.addWidget(self.checkBox_9)
        self.horizontalLayout_16.addLayout(self.horizontalLayout_7)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.label_30 = QtWidgets.QLabel(Dialog)
        self.label_30.setObjectName("label_30")
        self.horizontalLayout_8.addWidget(self.label_30)
        self.lineEdit_aget = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_aget.setObjectName("lineEdit_aget")
        self.horizontalLayout_8.addWidget(self.lineEdit_aget)
        self.label_28 = QtWidgets.QLabel(Dialog)
        self.label_28.setObjectName("label_28")
        self.horizontalLayout_8.addWidget(self.label_28)
        self.checkBox_8 = QtWidgets.QCheckBox(Dialog)
        self.checkBox_8.setObjectName("checkBox_8")
        self.horizontalLayout_8.addWidget(self.checkBox_8)
        self.horizontalLayout_16.addLayout(self.horizontalLayout_8)
        self.verticalLayout.addLayout(self.horizontalLayout_16)
        self.verticalLayout_2.addLayout(self.verticalLayout)
        self.line_3 = QtWidgets.QFrame(Dialog)
        self.line_3.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_3.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_3.setObjectName("line_3")
        self.verticalLayout_2.addWidget(self.line_3)
        self.label_11 = QtWidgets.QLabel(Dialog)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_11.setFont(font)
        self.label_11.setObjectName("label_11")
        self.verticalLayout_2.addWidget(self.label_11)
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.label_2 = QtWidgets.QLabel(Dialog)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_19.addWidget(self.label_2)
        self.lineEdit_2 = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_2.setObjectName("lineEdit_2")
        self.horizontalLayout_19.addWidget(self.lineEdit_2)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_19.addItem(spacerItem4)
        self.label_3 = QtWidgets.QLabel(Dialog)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_19.addWidget(self.label_3)
        self.lineEdit = QtWidgets.QLineEdit(Dialog)
        self.lineEdit.setObjectName("lineEdit")
        self.horizontalLayout_19.addWidget(self.lineEdit)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_19.addItem(spacerItem5)
        self.horizontalLayout_19.setStretch(0, 1)
        self.horizontalLayout_19.setStretch(1, 2)
        self.horizontalLayout_19.setStretch(2, 1)
        self.horizontalLayout_19.setStretch(3, 1)
        self.horizontalLayout_19.setStretch(4, 1)
        self.horizontalLayout_19.setStretch(5, 2)
        self.verticalLayout_2.addLayout(self.horizontalLayout_19)
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.label_4 = QtWidgets.QLabel(Dialog)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_24.addWidget(self.label_4)
        self.lineEdit_3 = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_3.setObjectName("lineEdit_3")
        self.horizontalLayout_24.addWidget(self.lineEdit_3)
        self.label_5 = QtWidgets.QLabel(Dialog)
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_24.addWidget(self.label_5)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem6)
        self.label_14 = QtWidgets.QLabel(Dialog)
        self.label_14.setObjectName("label_14")
        self.horizontalLayout_24.addWidget(self.label_14)
        self.lineEdit_6 = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_6.setObjectName("lineEdit_6")
        self.horizontalLayout_24.addWidget(self.lineEdit_6)
        self.label_15 = QtWidgets.QLabel(Dialog)
        self.label_15.setObjectName("label_15")
        self.horizontalLayout_24.addWidget(self.label_15)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem7)
        self.label_31 = QtWidgets.QLabel(Dialog)
        self.label_31.setObjectName("label_31")
        self.horizontalLayout_24.addWidget(self.label_31)
        self.oneSendMaxNumLineEdit = QtWidgets.QLineEdit(Dialog)
        self.oneSendMaxNumLineEdit.setObjectName("oneSendMaxNumLineEdit")
        self.horizontalLayout_24.addWidget(self.oneSendMaxNumLineEdit)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem8)
        self.horizontalLayout_24.setStretch(0, 1)
        self.horizontalLayout_24.setStretch(1, 1)
        self.horizontalLayout_24.setStretch(2, 1)
        self.horizontalLayout_24.setStretch(3, 2)
        self.horizontalLayout_24.setStretch(4, 1)
        self.horizontalLayout_24.setStretch(5, 1)
        self.horizontalLayout_24.setStretch(6, 1)
        self.horizontalLayout_24.setStretch(7, 2)
        self.horizontalLayout_24.setStretch(8, 1)
        self.horizontalLayout_24.setStretch(9, 1)
        self.horizontalLayout_24.setStretch(10, 2)
        self.verticalLayout_2.addLayout(self.horizontalLayout_24)
        self.line_4 = QtWidgets.QFrame(Dialog)
        self.line_4.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_4.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_4.setObjectName("line_4")
        self.verticalLayout_2.addWidget(self.line_4)
        self.label_12 = QtWidgets.QLabel(Dialog)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_12.setFont(font)
        self.label_12.setObjectName("label_12")
        self.verticalLayout_2.addWidget(self.label_12)
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.label_7 = QtWidgets.QLabel(Dialog)
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_20.addWidget(self.label_7)
        self.lineEdit_4 = QtWidgets.QLineEdit(Dialog)
        self.lineEdit_4.setObjectName("lineEdit_4")
        self.horizontalLayout_20.addWidget(self.lineEdit_4)
        self.pushButton_3 = QtWidgets.QPushButton(Dialog)
        self.pushButton_3.setObjectName("pushButton_3")
        self.horizontalLayout_20.addWidget(self.pushButton_3)
        self.verticalLayout_2.addLayout(self.horizontalLayout_20)
        self.line_5 = QtWidgets.QFrame(Dialog)
        self.line_5.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_5.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_5.setObjectName("line_5")
        self.verticalLayout_2.addWidget(self.line_5)
        self.label_13 = QtWidgets.QLabel(Dialog)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_13.setFont(font)
        self.label_13.setObjectName("label_13")
        self.verticalLayout_2.addWidget(self.label_13)
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.radioButton = QtWidgets.QRadioButton(Dialog)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.radioButton.setFont(font)
        self.radioButton.setChecked(True)
        self.radioButton.setObjectName("radioButton")
        self.horizontalLayout_21.addWidget(self.radioButton)
        self.radioButton_2 = QtWidgets.QRadioButton(Dialog)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.radioButton_2.setFont(font)
        self.radioButton_2.setObjectName("radioButton_2")
        self.horizontalLayout_21.addWidget(self.radioButton_2)
        self.pushButton = QtWidgets.QPushButton(Dialog)
        self.pushButton.setObjectName("pushButton")
        self.horizontalLayout_21.addWidget(self.pushButton)
        self.verticalLayout_2.addLayout(self.horizontalLayout_21)

        self.retranslateUi(Dialog)
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "机电伺服系统故障诊断与健康评估平台-数据采集客户端"))
        self.label.setText(_translate("Dialog", "机电伺服系统故障诊断与健康评估平台\n"
"          数据采集客户端"))
        self.label_10.setText(_translate("Dialog", "串口选择"))
        self.label_16.setText(_translate("Dialog", "串口地址："))
        self.comboBox.setItemText(0, _translate("Dialog", "usart3"))
        self.comboBox.setItemText(1, _translate("Dialog", "usart4"))
        self.pushButton_2.setText(_translate("Dialog", "开始采集"))
        self.checkBox.setText(_translate("Dialog", "接收控制指令"))
        self.label_8.setText(_translate("Dialog", "实际采集数据显示"))
        self.label_6.setText(_translate("Dialog", "q轴电流"))
        self.lineEdit_Iq.setText(_translate("Dialog", "0"))
        self.label_9.setText(_translate("Dialog", "A"))
        self.checkBox_2.setText(_translate("Dialog", "上传"))
        self.label_18.setText(_translate("Dialog", "力反馈："))
        self.lineEdit_fget.setText(_translate("Dialog", "0"))
        self.label_17.setText(_translate("Dialog", "N"))
        self.checkBox_3.setText(_translate("Dialog", "上传"))
        self.label_21.setText(_translate("Dialog", "位置指令信号："))
        self.lineEdit_xgive.setText(_translate("Dialog", "0"))
        self.label_20.setText(_translate("Dialog", "mm"))
        self.checkBox_5.setText(_translate("Dialog", "上传"))
        self.label_22.setText(_translate("Dialog", "实际位置信号："))
        self.lineEdit_xget.setText(_translate("Dialog", "0"))
        self.label_19.setText(_translate("Dialog", "mm"))
        self.checkBox_4.setText(_translate("Dialog", "上传"))
        self.label_26.setText(_translate("Dialog", "速度给定信号："))
        self.lineEdit_vgive.setText(_translate("Dialog", "0"))
        self.label_25.setText(_translate("Dialog", "mm/s"))
        self.checkBox_6.setText(_translate("Dialog", "上传"))
        self.label_23.setText(_translate("Dialog", "旋变速度信号："))
        self.lineEdit_vget.setText(_translate("Dialog", "0"))
        self.label_24.setText(_translate("Dialog", "mm/s"))
        self.checkBox_7.setText(_translate("Dialog", "上传"))
        self.label_27.setText(_translate("Dialog", "摩擦扭矩："))
        self.lineEdit_mocaget.setText(_translate("Dialog", "0"))
        self.label_29.setText(_translate("Dialog", "Nm"))
        self.checkBox_9.setText(_translate("Dialog", "上传"))
        self.label_30.setText(_translate("Dialog", "加速度反馈："))
        self.lineEdit_aget.setText(_translate("Dialog", "0"))
        self.label_28.setText(_translate("Dialog", "m/s2"))
        self.checkBox_8.setText(_translate("Dialog", "上传"))
        self.label_11.setText(_translate("Dialog", "远程参数设置"))
        self.label_2.setText(_translate("Dialog", "服务器地址："))
        self.lineEdit_2.setText(_translate("Dialog", "127.0.0.1"))
        self.label_3.setText(_translate("Dialog", "端口："))
        self.lineEdit.setText(_translate("Dialog", "8000"))
        self.label_4.setText(_translate("Dialog", "上传间隔："))
        self.lineEdit_3.setText(_translate("Dialog", "0"))
        self.label_5.setText(_translate("Dialog", "ms"))
        self.label_14.setText(_translate("Dialog", "读取间隔："))
        self.lineEdit_6.setText(_translate("Dialog", "5"))
        self.label_15.setText(_translate("Dialog", "ms"))
        self.label_31.setText(_translate("Dialog", "单次最大上传个数："))
        self.oneSendMaxNumLineEdit.setText(_translate("Dialog", "100"))
        self.label_12.setText(_translate("Dialog", "本地上传文件选择"))
        self.label_7.setText(_translate("Dialog", "选择本地文件："))
        self.pushButton_3.setText(_translate("Dialog", "选择"))
        self.label_13.setText(_translate("Dialog", "上传控制"))
        self.radioButton.setText(_translate("Dialog", "实时数据"))
        self.radioButton_2.setText(_translate("Dialog", "本地文件"))
        self.pushButton.setText(_translate("Dialog", "开始上传"))
