
302f5ae5f015cf564b68627a97ff40a773810450	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"72e418239e1c0c7bc0a707b8764b4757\"}","integrity":"sha512-Ok82LMdMB6m6sa2/SZvq98ZsbmBBFHObAyMQX8D2PjAIHC5k4vM6FGTVFAOQ0VfepGZUNclZmDiqwaDsooKcHg==","time":1754200395818,"size":26691}