# -*- coding: utf-8 -*-

import os,xlrd
import random,json,requests,time,datetime,sys

from PyQt5 import QtWidgets
from PyQt5.QtWidgets import QMessageBox
from PyQt5.Qt import QThread
from PyQt5.QtCore import pyqtSignal

from PHMClient import Ui_Dialog


# 是否可以远程控制的标志
canControlFlag = True

# 保存所有数据的队列
allDataQueue = []

class Thread_1(QThread):  # 线程1
    _signal = pyqtSignal(str,str,str,str,str)
    def __init__(self):
        super().__init__()
    def run(self):
        pass

class SendThread(QThread):
    def __init__(self,threadName):
        '''
        上传数据线程 从全局队列allDataQueue中依此读取数据
        :param threadName: 线程名称
        :param targetURL: 远程URL
        '''
        super().__init__()
        self.name = threadName
        self.host = "127.0.0.1"
        self.port = "8000"
        self.startFlag = False
        self.sendDelayTime = 0.001
        self.countNum = 0

    def readOneDataFromQueue(self) -> dict:
        '''
        从数据队列中读取头数据，如果长度为0 必须等待之后在发送
        因此向队列中放数据和读数据必须在两个线程中，不然会一直在读数据的while循环中
        :return: 读取的头数据
        '''
        global allDataQueue
        # 列表为空 不能进行发送 需要等待数据读取
        while len(allDataQueue) == 0:
            time.sleep(0.0001)
        print("队列剩余长度",len(allDataQueue)-1,end='\t')
        return allDataQueue.pop(0)

    def uploadOneData(self,postDataSource: dict) -> None:
        '''
        输入一条数据，将数据上传至服务器
        :param postDataSource:单条数据
        :return:无
        '''
        #print("开始上传数据",postDataSource)
        postData = json.dumps(postDataSource)
        myHeader = {"user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
        req = requests.post(url='http://'+self.host+':'+self.port+'/phm/upData/', data=str(postData), headers=myHeader)
        code = eval(req.text)
        if code['code'] == 200:
            self.countNum += 1
            print("上传计数",self.countNum)
            print('成功发送数据---', postData, '---返回值为---', code['controlData'])

    def run(self):
        print("++++++上传线程开始工作++++++")
        while self.startFlag:
            self.uploadOneData(self.readOneDataFromQueue())
            time.sleep(self.sendDelayTime)

class ReadDataThread(QThread):
    _signal = pyqtSignal(str, str,str,str, str, str, str,str)
    def __init__(self,threadName):
        '''
        读取数据线程 模拟数据读取过程 读取之后保存在全局变量 allDataQueue 中
        :param threadName: 线程名称
        '''
        super().__init__()
        self.name = threadName
        self.startFlag = False
        self.sendFile = r'2-0.1-0-25-0.0005.xlsx'
        self.readDelayTime = 0.001
        # 读取的数据个数计数
        self.countNum = 0

    def read_excel(self,xlsxName: str) -> tuple:
        '''
        读取一个xlxs文件
        :param xlsxName: 文件名
        :return:读取的数据列表，依此为给定位移，实际位移，负载力，Iq
        '''
        workbook = xlrd.open_workbook(xlsxName)
        sheet1_name = workbook.sheet_names()[0]
        sheet1 = workbook.sheet_by_name(sheet1_name)
        xGive = sheet1.col_values(9)[1:]
        xGet = sheet1.col_values(11)[1:]
        fGet = sheet1.col_values(3)[1:]
        IqGet = sheet1.col_values(13)[1:]
        #fGive = sheet1.col_values(1)[1:]
        vGive = sheet1.col_values(21)[1:]
        vGet = sheet1.col_values(19)[1:]
        #IGive = sheet1.col_values(23)[1:]
        aGet = sheet1.col_values(27)[1:]# 加速度
        moCaGet = sheet1.col_values(37)[1:]
        return xGive, xGet, fGet,IqGet,vGive,vGet,moCaGet,aGet

    def putOneDataInQueue(self,data: dict) -> None:
        '''
        向数据队列中加入一条数据，该函数和readOneDataFromQueue()必须处于两个线程中运行
        :param data: 需要放入的数据
        :return: 无
        '''
        global allDataQueue
        allDataQueue.append(data)
        self.countNum += 1
        print("写入计数",self.countNum)

    def sendOneFile(self,fileName):
        '''
        发送一个xlxs文件
        :param fileName:文件名
        :return:
        '''
        self.xGive, self.xGet, self.fGet, self.IqGet,vGive,vGet,moCaGet,aGet= self.read_excel(fileName)
        print("文件长度为---",len(self.xGive))
        for i in range(len(self.xGive)):
            if not self.startFlag:
                break
            postData = {"time": str(datetime.datetime.now()), "type": '1', "Iq": str(self.IqGet[i]),'x_give': str(self.xGive[i]), 'x_get': str(self.xGet[i]), 'f_get': str(self.fGet[i])}
            # 单条数据读取时间控制
            self._signal.emit(str(self.xGive[i]),str(self.xGet[i]),str(self.fGet[i]),str(self.IqGet[i]),str(vGive[i]),str(vGet[i]),str(moCaGet[i]),str(aGet[i]))
            time.sleep(self.readDelayTime)
            self.putOneDataInQueue(postData)

    def run(self):
        print("++++++读数据线程开始工作++++++")
        while self.startFlag:
            self.sendOneFile(self.sendFile)
            break

class PHMClientMain(QtWidgets.QDialog,Ui_Dialog):
    def __init__(self):
        super(PHMClientMain, self).__init__()
        self.setupUi(self)
        self.flag = False
        self.readDelayTime = 0.001
        self.sendFileName = ''
        print("__init__")
        self.pushButton_3.clicked.connect(self.getFileName)
        self.pushButton.clicked.connect(self.startSend)
        self.sendThread = SendThread('Read data and save to queue')
        self.readDataThread = ReadDataThread('Read from queue and send it to server')
        self.readDataThread.readDelayTime = 0.001
        self.readDataThread._signal.connect(self.updateData)

    def getFileName(self):
        '''
        读取文件按键响应函数
        :return:
        '''
        print("按下读取文件按键")
        self.sendFileName,fileType = QtWidgets.QFileDialog.getOpenFileName(self, "选取文件", os.getcwd(),"Excel Files(*.xlsx);;All Files(*)")
        self.lineEdit_4.setText(self.sendFileName)
        print(self.sendFileName,fileType)


    def startSend(self):
        '''
        开始上传按键响应函数
        :return:
        '''
        # 选择实时数据
        self.flag = not self.flag
        if self.radioButton.isChecked():
            pass
        else:
            if self.sendFileName == '':
                reply = QMessageBox.information(self,"标题","还未选择上传文件",QMessageBox.Yes | QMessageBox.No)
                self.flag = not self.flag
                return
            self.readDataThread.sendFile = self.sendFileName
            self.readDataThread.readDelayTime = int(self.lineEdit_6.text()) / 1000.0
            self.readDataThread.startFlag = self.flag
            self.readDataThread.start()

        if self.flag:
            self.pushButton.setText("停止上传")
        else:
            self.pushButton.setText("开始上传")
        print(self.flag)
        self.sendThread.startFlag = self.flag
        self.sendThread.host = self.lineEdit_2.text()
        self.sendThread.port = self.lineEdit.text()
        self.sendThread.sendDelayTime = int(self.lineEdit_3.text())/1000.0
        self.sendThread.start()
        #print(self.radioButton.isChecked())



    def updateData(self,xGive,xGet,fGet,IqGet,vGive,vGet,moCaGet,aGet):
        global canControlFlag
        self.lineEdit_xgive.setText(str(xGive))
        self.lineEdit_xget.setText(str(xGet))
        self.lineEdit_fget.setText(str(fGet))
        self.lineEdit_Iq.setText(str(IqGet))
        self.lineEdit_vgive.setText(str(vGive))
        self.lineEdit_vget.setText(str(vGet))
        self.lineEdit_mocaget.setText(str(moCaGet))
        self.lineEdit_aget.setText(str(aGet))
        if self.checkBox.checkState() == 0:
            canControlFlag = False
        else:
            canControlFlag = True
        print(self.checkBox.checkState())



if __name__=='__main__':
    # postData()
    app = QtWidgets.QApplication(sys.argv)
    ui = PHMClientMain()
    ui.show()
    sys.exit(app.exec_())