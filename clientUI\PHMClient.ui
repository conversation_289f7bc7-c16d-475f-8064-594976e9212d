<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author>AlwaysSun</author>
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>703</width>
    <height>728</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>机电伺服系统故障诊断和健康评估平台-数据采集客户端</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>buaa.png</normaloff>buaa.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="1,0,3,1">
     <item>
      <widget class="QLabel" name="label_32">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap>buaa_s.png</pixmap>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_8">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <pointsize>14</pointsize>
        </font>
       </property>
       <property name="text">
        <string>机电伺服系统故障诊断和健康评估平台
          数据采集客户端</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_9">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_10">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>串口选择</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_23" stretch="2,0,2,0,0">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_22">
       <item>
        <widget class="QLabel" name="label_16">
         <property name="text">
          <string>串口地址：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBox">
         <item>
          <property name="text">
           <string>usart3</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>usart4</string>
          </property>
         </item>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_2">
       <property name="text">
        <string>开始采集</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBox">
       <property name="text">
        <string>接收控制指令</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_8">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>实际采集数据显示</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>15</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_13">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>q轴电流</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_Iq">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>A</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_2">
           <property name="text">
            <string>上传</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_18">
           <property name="text">
            <string>力反馈：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_fget">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_17">
           <property name="text">
            <string>N</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_3">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="text">
            <string>上传</string>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_14">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_21">
           <property name="text">
            <string>位置指令信号：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_xgive">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_20">
           <property name="text">
            <string>mm</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_5">
           <property name="text">
            <string>上传</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QLabel" name="label_22">
           <property name="text">
            <string>实际位置信号：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_xget">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_19">
           <property name="text">
            <string>mm</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_4">
           <property name="text">
            <string>上传</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_15">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QLabel" name="label_26">
           <property name="text">
            <string>速度给定信号：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_vgive">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_25">
           <property name="text">
            <string>mm/s</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_6">
           <property name="text">
            <string>上传</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QLabel" name="label_23">
           <property name="text">
            <string>旋变速度信号：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_vget">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_24">
           <property name="text">
            <string>mm/s</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_7">
           <property name="text">
            <string>上传</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_16">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <widget class="QLabel" name="label_27">
           <property name="text">
            <string>摩擦扭矩：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_mocaget">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_29">
           <property name="text">
            <string>Nm</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_9">
           <property name="text">
            <string>上传</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <item>
          <widget class="QLabel" name="label_30">
           <property name="text">
            <string>加速度反馈：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_aget">
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_28">
           <property name="text">
            <string>m/s2</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_8">
           <property name="text">
            <string>上传</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_11">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>远程参数设置</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_19" stretch="1,2,1,1,1,2">
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>服务器地址：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_2">
       <property name="text">
        <string>101.42.93.111</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>端口：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit">
       <property name="text">
        <string>8000</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_24" stretch="1,1,1,2,1,1,1,2,1,1,2">
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>上传间隔：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_3">
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>ms</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_14">
       <property name="text">
        <string>读取间隔：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_6">
       <property name="text">
        <string>5</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_15">
       <property name="text">
        <string>ms</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_7">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_31">
       <property name="text">
        <string>单次最大上传个数：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="oneSendMaxNumLineEdit">
       <property name="text">
        <string>100</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_4">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_12">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>本地上传文件选择</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_20">
     <item>
      <widget class="QLabel" name="label_7">
       <property name="text">
        <string>选择本地文件：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit_4"/>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_3">
       <property name="text">
        <string>选择</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line_5">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_13">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>上传控制</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_21">
     <item>
      <widget class="QRadioButton" name="radioButton">
       <property name="font">
        <font>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="text">
        <string>实时数据</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButton_2">
       <property name="font">
        <font>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="text">
        <string>本地文件</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton">
       <property name="text">
        <string>开始上传</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
 <designerdata>
  <property name="gridDeltaX">
   <number>10</number>
  </property>
  <property name="gridDeltaY">
   <number>10</number>
  </property>
  <property name="gridSnapX">
   <bool>true</bool>
  </property>
  <property name="gridSnapY">
   <bool>true</bool>
  </property>
  <property name="gridVisible">
   <bool>true</bool>
  </property>
 </designerdata>
</ui>
